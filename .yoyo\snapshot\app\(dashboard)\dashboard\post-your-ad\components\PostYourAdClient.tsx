"use client";
import React from "react";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { formDataSchema } from "../validations/formDataSchema";
import useFormStore from "../store/useFormStore";
import useUploadMediaStore from "../store/useUploadMediaStore";
import useQAndAStore from "../store/useFAQStore";
import UploadPhotos from "./UploadPhotos";
import ReorderPhotos from "./ReorderPhotos";
import UploadVideos from "./UploadVideos";
import UploadAttachments from "./UploadAttachments";
import Modal from "@/components/Modal";
import FAQs from "@/components/Faqs";
import { auctionFAQs } from "@/data/auctionFAQs";

type Props = {
  initialFormData?: any;
  steps: React.ReactElement[];
};

const PostYourAdClient = ({ initialFormData = {}, steps }: Props) => {
  const methods = useForm({
    resolver: zodResolver(formDataSchema),
    defaultValues: {
      category: { main: "", subcategory: "" },
      details: { condition: "" },
      price: { pricingOption: "", price: 0 },
      createAccount: { bankName: "", accountHolder: "", accountNumber: "" },
      titleAndDescription: { title: "", description: "" },
      uploadMedia: {
        uploadPhotos: false,
        uploadVideos: false,
        uploadAttachments: false,
      },
      location: { province: "", city: "", suburb: "", customLocation: "" },
      promoteYourAd: { promotionDuration: "" },
      reviewYourAd: "",
      ...initialFormData,
    },
  });

  const { currentStepIndex} = useFormStore();
  const { goToMediaType } = useUploadMediaStore();
  const { showFAQs, setShowFAQs } = useQAndAStore();

  const getMediaComponent = (goToMediaType) => {
    const mediaComponents = {
      photos: <UploadPhotos />,
      reorder: <ReorderPhotos />,
      videos: <UploadVideos />,
      attachments: <UploadAttachments />,
      none: steps[5],
    };
    return mediaComponents[goToMediaType] || steps[5];
  };

  const renderStep = () => {
    const enhancedSteps = steps.map((step: React.ReactElement, index: number) => {
      if (index === 0) {
        return React.cloneElement(step);
      }
      return step;
    });
   
    if (currentStepIndex === 5) return getMediaComponent(goToMediaType);
    if (currentStepIndex === 2 && showFAQs)
      return (
        <Modal
          showModal={showFAQs}
          setShowModal={setShowFAQs}
          modalContent={<FAQs faqs={auctionFAQs} />}
        />
      );
    return enhancedSteps[currentStepIndex];
  };

  return <FormProvider {...methods}>{renderStep()}</FormProvider>;
};

export default PostYourAdClient;