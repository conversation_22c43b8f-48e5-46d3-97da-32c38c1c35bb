@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.categoriesBtnContainer {
  margin-bottom: rem(8) !important;
  
}

.searchFields {
  // margin-bottom: rem(30);
  .searchTerm {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: rem(20);

    .searchTermInput {
    }
  }
  .searchLocation {
    display: flex;
    flex-direction: column;
    align-items: center;

margin-bottom: rem(20);
    .searchLocationInput {
      
    }
    .errorMessage {
     height:0 !important;
    }
  }

  .errorMessage {
    color: $danger;
    margin-bottom: rem(8);
  }
}

.searchFields {
  // margin-bottom: rem(20) !important;
  margin-top: rem(4) !important;
  .searchTerm {
    display: flex;
    flex-direction: column;
    align-items: center;
    // margin-bottom: rem(20);

    .searchTermInput {
    }
  }
  .searchLocation {
    display: flex;
    flex-direction: column;
    align-items: center;

    .searchLocationInput {
      // margin-bottom: rem(10);
    }
  }

  .errorMessage {
    color: $danger;
    margin-bottom: rem(8);
  }
}
.searchButton {
  .search {
    background-color: $white-two;
    box-shadow: none;
  }

  .search:hover {
    background-color: $white-one;
  }
}
