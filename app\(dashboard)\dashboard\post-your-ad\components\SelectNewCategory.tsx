"use client";

import React from "react";
import Button from "@/components/Buttons";
import useFormStore from "../store/useFormStore";
import { FormWrapper } from "./FormWrapper";
import styles from "./SelectNewCategory.module.scss";

const SelectNewCategory = () => {
  const { goTo, currentStepIndex, setCategoryPreviouslySelected } =
    useFormStore();

  const selectNewCategory = () => {
    // Reset the category selection flag and go to the original category selection step
    setCategoryPreviouslySelected(false);
    goTo(0);
  };

  const continueToNextStep = () => {
    // Go to the step after category selection
    goTo(1);
  };

  return (
    <FormWrapper title="Category Selection" selectOpen={true}>
      <div className={styles.container}>
        <h2 className={styles.title}>Category Selection</h2>
        <p className={styles.description}>
          Would you like to select a new category or continue with your current
          selection?
        </p>

        <div className={styles.buttonContainer}>
          <Button
            buttonChildren="Select New Category"
            buttonType="primary"
            buttonSize="large"
            onClick={selectNewCategory}
            dashboard
          />

          <Button
            buttonChildren="Continue with Current Category"
            buttonType="normal"
            buttonSize="large"
            onClick={continueToNextStep}
            dashboard
          />
        </div>
      </div>
    </FormWrapper>
  );
};

export default SelectNewCategory;
