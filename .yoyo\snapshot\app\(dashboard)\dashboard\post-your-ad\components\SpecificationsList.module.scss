 @use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
 .details {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: rem(648);

    .detail {
      display: flex;
      align-items: center;
      margin-bottom: rem(20);
      width: rem(480);

      .detailButtons {
        display: flex;
         gap: rem(8);
        margin-right: rem(16);
        .detailButton {
          display: flex;
          justify-content: center;
          align-items: center;
          width: rem(40);
          height: rem(40);
          border: none;
         
          background-color: $white-one;
        }

        .detailButton:hover {
          box-shadow: $shadow-one;
          transform: scale(1.1);
        }
        .editButtonContainer {
        }

        .deleteButtonContainer {
          margin-left: rem(16);
        }
      }

      .detailText {
        font-weight: normal;
      }
    }

    .editMode {
      position: relative;
      width: rem(514);
      margin-bottom: rem(16);

      .editDetail {
        width: rem(514);
        height: rem(40);
        text-align: start;
        // padding-top: rem(12);
        // padding-left: rem(32);
        // padding-right: rem(48);
        // margin-left: rem(69);

        color: $black-one;
      }

      .submitButton {
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        width: rem(40);
        height: rem(40);
        border-radius: 50%;
        transform: translateY(-50%);
        right: rem(8);
        z-index: 10;
        background-color: $white-one;
        cursor: pointer;
        box-shadow:
          10px 10px 20px 0px rgba(169, 196, 203, 0.5),
          5px 5px 10px 0px rgba(169, 196, 203, 0.25);

        .iconContainer {
          margin-top: rem(4);
        }
      }
    }
  }