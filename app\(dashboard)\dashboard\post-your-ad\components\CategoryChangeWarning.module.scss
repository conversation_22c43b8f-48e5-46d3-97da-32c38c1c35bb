@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: rem(32);
  max-width: rem(600);
  width: 90vw;
  background-color: $white;
  border-radius: rem(16);
  box-shadow: 0 rem(8) rem(32) rgba(0, 0, 0, 0.1);
  text-align: center;
  
  @include only(tablet) {
    padding: rem(40);
    width: rem(560);
  }
}

.iconContainer {
  margin-bottom: rem(24);
  
  .warningIcon {
    font-size: rem(48);
    filter: drop-shadow(0 rem(2) rem(4) rgba(0, 0, 0, 0.1));
    
    @include only(tablet) {
      font-size: rem(56);
    }
  }
}

.title {
  color: $danger;
  font-size: $h5;
  font-weight: 600;
  margin-bottom: rem(24);
  line-height: 1.3;
  
  @include only(tablet) {
    font-size: $h4;
    margin-bottom: rem(32);
  }
}

.content {
  margin-bottom: rem(32);
  text-align: left;
  width: 100%;
  
  .description {
    color: $dark-gray;
    font-size: $body;
    line-height: 1.5;
    margin-bottom: rem(16);
  }
  
  .list {
    color: $dark-gray;
    font-size: $body;
    line-height: 1.6;
    margin: rem(16) 0;
    padding-left: rem(20);
    
    li {
      margin-bottom: rem(8);
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .confirmText {
    color: $danger;
    font-size: $body;
    font-weight: 500;
    line-height: 1.5;
    margin-top: rem(20);
    text-align: center;
  }
}

.buttonContainer {
  display: flex;
  flex-direction: column;
  gap: rem(12);
  width: 100%;
  
  @include only(tablet) {
    flex-direction: row;
    gap: rem(16);
    justify-content: center;
  }
}
