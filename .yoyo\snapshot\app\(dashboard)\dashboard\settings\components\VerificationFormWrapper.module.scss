@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;

  align-items: center;
  width: rem(311);

  @include only(largeDesktop) {
    width: rem(1098);
    margin-left: rem(-48);
  }

  .form {
    display: flex;
    flex-direction: column;

    align-items: center;
    width: rem(311);

    @include only(largeDesktop) {
      width: rem(1098);
      margin-left: rem(-48);
    }
    .titleContainer {
      display: flex;
      justify-content: center;
      margin-top: rem(52);
      margin-bottom: rem(64);
      width: 100%;
      .title {
        color: $danger;
        font-size: $h4;
        font-weight: 600;
      }
    }

    .children {
      display: flex;
      justify-content: center;
      align-items: center;
      height: fit-content;
      margin-bottom: rem(64);
    }

    .buttons {
      display: flex;
      flex-direction: column;
      margin-bottom: rem(120);

      .proceedButton {
        margin-bottom: rem(20);
      }
    }
  }

  .progressBar {
    position: fixed;
    bottom: 0;
    z-index: 5;
  }
}
